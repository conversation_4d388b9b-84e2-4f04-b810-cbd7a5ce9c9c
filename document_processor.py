#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
保险知识库文档处理器
用于将MD格式的保险条款文档处理成结构化的知识块
"""

import re
import json
from typing import List, Dict, Any
from dataclasses import dataclass
import hashlib

@dataclass
class DocumentChunk:
    """文档块数据结构"""
    chunk_id: str
    title: str
    level: int
    content: str
    keywords: List[str]
    related_terms: List[str]
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'chunk_id': self.chunk_id,
            'title': self.title,
            'level': self.level,
            'content': self.content,
            'keywords': self.keywords,
            'related_terms': self.related_terms,
            'metadata': self.metadata
        }

class InsuranceDocumentProcessor:
    """保险文档处理器"""
    
    def __init__(self):
        # 保险相关关键词词典
        self.insurance_keywords = {
            '保障类型': ['意外伤害', '重大疾病', '轻症疾病', '身故保险金', '医疗保险'],
            '医疗相关': ['医院', '确诊', '治疗', '手术', '康复', '护理'],
            '责任条款': ['保险责任', '责任免除', '等待期', '保险期间'],
            '理赔相关': ['给付', '赔付', '理赔', '受益人', '保险金额'],
            '风险因素': ['车祸', '交通事故', '意外', '疾病', '伤害', '死亡']
        }
        
        # 专业术语定义模式
        self.term_patterns = [
            r'(\w+)指(.+?)。',  # "术语指定义。"
            r'(\w+)是指(.+?)。', # "术语是指定义。"
            r'(\w+)：(.+?)。',   # "术语：定义。"
        ]
    
    def extract_keywords(self, text: str) -> List[str]:
        """从文本中提取关键词"""
        keywords = []
        for category, terms in self.insurance_keywords.items():
            for term in terms:
                if term in text:
                    keywords.append(term)
        return list(set(keywords))
    
    def extract_terms(self, text: str) -> List[str]:
        """提取专业术语定义"""
        terms = []
        for pattern in self.term_patterns:
            matches = re.findall(pattern, text)
            for match in matches:
                if isinstance(match, tuple) and len(match) >= 1:
                    terms.append(match[0])
        return terms
    
    def generate_chunk_id(self, title: str, content: str) -> str:
        """生成唯一的块ID"""
        content_hash = hashlib.md5((title + content).encode('utf-8')).hexdigest()[:8]
        return f"chunk_{content_hash}"
    
    def parse_markdown_structure(self, content: str) -> List[Dict[str, Any]]:
        """解析Markdown文档结构"""
        lines = content.split('\n')
        sections = []
        current_section = None
        
        for line in lines:
            # 检测标题级别
            if line.startswith('#'):
                # 保存上一个section
                if current_section:
                    sections.append(current_section)
                
                # 计算标题级别
                level = len(line) - len(line.lstrip('#'))
                title = line.lstrip('#').strip()
                
                current_section = {
                    'title': title,
                    'level': level,
                    'content_lines': [],
                    'line_number': len(sections) + 1
                }
            elif current_section:
                # 添加内容行
                if line.strip():  # 忽略空行
                    current_section['content_lines'].append(line)
        
        # 添加最后一个section
        if current_section:
            sections.append(current_section)
        
        return sections
    
    def process_document(self, file_path: str) -> List[DocumentChunk]:
        """处理文档并返回结构化块"""
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 提取产品名称
        first_line = content.split('\n')[0]
        product_name = first_line.replace('#', '').strip()
        
        # 解析文档结构
        sections = self.parse_markdown_structure(content)
        chunks = []
        
        for section in sections:
            # 合并内容
            section_content = '\n'.join(section['content_lines'])
            
            # 跳过过短的内容
            if len(section_content.strip()) < 10:
                continue
            
            # 提取关键词和术语
            keywords = self.extract_keywords(section_content)
            terms = self.extract_terms(section_content)
            
            # 创建文档块
            chunk = DocumentChunk(
                chunk_id=self.generate_chunk_id(section['title'], section_content),
                title=section['title'],
                level=section['level'],
                content=section_content,
                keywords=keywords,
                related_terms=terms,
                metadata={
                    'product': product_name,
                    'section_number': section['line_number'],
                    'content_length': len(section_content),
                    'source_file': file_path
                }
            )
            
            chunks.append(chunk)
        
        return chunks
    
    def save_processed_chunks(self, chunks: List[DocumentChunk], output_file: str):
        """保存处理后的文档块"""
        chunks_data = [chunk.to_dict() for chunk in chunks]
        
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(chunks_data, f, ensure_ascii=False, indent=2)
        
        print(f"已保存 {len(chunks)} 个文档块到 {output_file}")
    
    def print_sample_chunks(self, chunks: List[DocumentChunk], max_samples: int = 5):
        """打印示例文档块"""
        print(f"\n=== 文档处理结果示例 (共{len(chunks)}个块) ===\n")
        
        for i, chunk in enumerate(chunks[:max_samples]):
            print(f"【块 {i+1}】")
            print(f"ID: {chunk.chunk_id}")
            print(f"标题: {chunk.title}")
            print(f"级别: {chunk.level}")
            print(f"关键词: {chunk.keywords}")
            print(f"术语: {chunk.related_terms}")
            print(f"内容预览: {chunk.content[:100]}...")
            print(f"元数据: {chunk.metadata}")
            print("-" * 50)

def main():
    """主函数"""
    processor = InsuranceDocumentProcessor()
    
    # 处理文档
    input_file = "百年人寿保险股份有限公司_百年易核版（2024版）终身重大疾病保险_条款_e4b1ec62-bf02-47d3-a8ca-7405e0116ceb.md"
    
    try:
        chunks = processor.process_document(input_file)
        
        # 保存结果
        output_file = "1.json"
        processor.save_processed_chunks(chunks, output_file)
        
        # 显示示例
        processor.print_sample_chunks(chunks)
        
    except FileNotFoundError:
        print(f"错误：找不到文件 {input_file}")
    except Exception as e:
        print(f"处理文档时出错：{e}")

if __name__ == "__main__":
    main()
