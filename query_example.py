#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查询示例：展示如何使用处理后的文档块进行智能检索
"""

import json
from typing import List, Dict, Any

def load_processed_chunks(file_path: str) -> List[Dict[str, Any]]:
    """加载处理后的文档块"""
    with open(file_path, 'r', encoding='utf-8') as f:
        return json.load(f)

def search_by_keywords(chunks: List[Dict[str, Any]], query_keywords: List[str]) -> List[Dict[str, Any]]:
    """基于关键词搜索相关文档块"""
    relevant_chunks = []
    
    for chunk in chunks:
        # 检查关键词匹配
        chunk_keywords = chunk.get('keywords', [])
        content = chunk.get('content', '')
        title = chunk.get('title', '')
        
        # 计算匹配分数
        score = 0
        matched_keywords = []
        
        for keyword in query_keywords:
            if keyword in chunk_keywords:
                score += 2  # 关键词匹配权重更高
                matched_keywords.append(keyword)
            elif keyword in content:
                score += 1  # 内容匹配
                matched_keywords.append(keyword)
            elif keyword in title:
                score += 1.5  # 标题匹配
                matched_keywords.append(keyword)
        
        if score > 0:
            chunk_with_score = chunk.copy()
            chunk_with_score['relevance_score'] = score
            chunk_with_score['matched_keywords'] = matched_keywords
            relevant_chunks.append(chunk_with_score)
    
    # 按相关性分数排序
    relevant_chunks.sort(key=lambda x: x['relevance_score'], reverse=True)
    return relevant_chunks

def simulate_user_query():
    """模拟用户查询：有没有可以保障我出车祸的保险？"""
    
    print("=== 用户查询模拟 ===")
    print("用户问题：有没有可以保障我出车祸的保险？")
    print()
    
    # 加载处理后的文档
    chunks = load_processed_chunks("1.json")
    print(f"已加载 {len(chunks)} 个文档块")
    
    # 提取查询关键词
    query_keywords = ["车祸", "交通事故", "意外伤害", "意外", "保障", "保险"]
    print(f"查询关键词：{query_keywords}")
    print()
    
    # 搜索相关文档块
    relevant_chunks = search_by_keywords(chunks, query_keywords)
    
    print(f"=== 检索结果 (前5个最相关的块) ===")
    print()
    
    for i, chunk in enumerate(relevant_chunks[:5]):
        print(f"【结果 {i+1}】")
        print(f"相关性分数: {chunk['relevance_score']}")
        print(f"匹配关键词: {chunk['matched_keywords']}")
        print(f"标题: {chunk['title']}")
        print(f"内容: {chunk['content'][:200]}...")
        print(f"关键词: {chunk['keywords']}")
        print("-" * 60)
    
    # 模拟DeepSeek推理过程
    print("\n=== DeepSeek推理过程模拟 ===")
    
    if relevant_chunks:
        top_chunk = relevant_chunks[0]
        print("基于检索到的最相关内容：")
        print(f"标题：{top_chunk['title']}")
        print(f"内容：{top_chunk['content']}")
        print()
        
        print("AI推理结果：")
        print("根据保险条款，这款百年易核版终身重大疾病保险确实可以保障车祸相关风险：")
        print()
        print("1. 意外伤害保障：")
        print("   - 条款明确定义'意外伤害'为'遭受外来的、突发的、非本意的、非疾病的使身体受到伤害的客观事件'")
        print("   - 车祸完全符合这个定义")
        print()
        print("2. 等待期豁免：")
        print("   - 因意外伤害导致的保险事故不受180天等待期限制")
        print("   - 车祸导致的伤害可以立即获得保障")
        print()
        print("3. 保障范围：")
        print("   - 如果车祸导致重大疾病，可获得100%基本保险金额赔付")
        print("   - 如果车祸导致轻症疾病，可获得20%基本保险金额赔付")
        print("   - 如果车祸导致身故，可获得身故保险金")
        print()
        print("建议：这款保险适合您的车祸保障需求，但请注意免责条款中的酒驾等情况不在保障范围内。")

def show_document_structure():
    """展示文档处理后的结构"""
    chunks = load_processed_chunks("1.json")
    
    print("=== 文档结构分析 ===")
    print(f"总文档块数：{len(chunks)}")
    
    # 按级别统计
    level_stats = {}
    for chunk in chunks:
        level = chunk['level']
        level_stats[level] = level_stats.get(level, 0) + 1
    
    print("标题级别分布：")
    for level, count in sorted(level_stats.items()):
        print(f"  级别 {level}: {count} 个块")
    
    # 关键词统计
    all_keywords = []
    for chunk in chunks:
        all_keywords.extend(chunk.get('keywords', []))
    
    keyword_freq = {}
    for keyword in all_keywords:
        keyword_freq[keyword] = keyword_freq.get(keyword, 0) + 1
    
    print("\n高频关键词 (前10个)：")
    sorted_keywords = sorted(keyword_freq.items(), key=lambda x: x[1], reverse=True)
    for keyword, freq in sorted_keywords[:10]:
        print(f"  {keyword}: {freq} 次")

if __name__ == "__main__":
    print("保险知识库查询示例")
    print("=" * 50)
    
    # 展示文档结构
    show_document_structure()
    print()
    
    # 模拟用户查询
    simulate_user_query()
